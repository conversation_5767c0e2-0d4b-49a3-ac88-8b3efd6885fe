#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify wandb integration with LLaOA training.
This script tests the wandb configuration and initialization without running full training.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add LLaOA to path
sys.path.insert(0, str(Path(__file__).parent))

from llaoa.train.wandb_utils import WandbConfig, initialize_wandb, log_metrics, log_model_info, finish_wandb


def test_wandb_config():
    """Test WandbConfig initialization and configuration."""
    print("Testing WandbConfig...")
    
    # Test with default configuration
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = os.path.join(temp_dir, "llaoa_projector_training_20250623_123456")
        os.makedirs(output_dir, exist_ok=True)
        
        config = WandbConfig(output_dir=output_dir)
        
        print(f"✓ Project name: {config.project_name}")
        print(f"✓ Run name: {config.run_name}")
        print(f"✓ Logs directory: {config.logs_dir}")
        print(f"✓ Offline mode: {config.offline}")
        
        # Test environment setup
        config.setup_environment()
        print(f"✓ WANDB_MODE: {os.environ.get('WANDB_MODE', 'not set')}")
        print(f"✓ WANDB_DIR: {os.environ.get('WANDB_DIR', 'not set')}")
        
        # Test system info
        system_info = config.get_system_info()
        print(f"✓ System info keys: {list(system_info.keys())}")
        
        return True


def test_wandb_initialization():
    """Test wandb initialization without actual training."""
    print("\nTesting wandb initialization...")
    
    try:
        import wandb
        wandb_available = True
    except ImportError:
        print("⚠ wandb not available, skipping initialization test")
        return True
    
    # Create mock arguments
    class MockArgs:
        def __init__(self):
            self.model_path = None
            self.model_base = "./models/llama-2-7b-chat-hf"
            self.compass_model_path = "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
            self.feature_type = "gene_level"
            self.projector_type = "mlp2x_gelu"
            self.tune_projector_only = True
            
            self.rna_seq_path = "./data/dataset_01/rnaseq_tpm.tsv"
            self.qa_json_path = "./data/dataset_01/qa_pairs.json"
            self.sample_id_col = "sample_id"
            self.max_length = 512
            self.validation_split = 0.1
            
            self.output_dir = "./test_checkpoints/llaoa_wandb_test_20250623_123456"
            self.num_train_epochs = 1.0
            self.per_device_train_batch_size = 2
            self.learning_rate = 1e-5
            self.seed = 42
            self.wandb_enabled = True
            self.wandb_offline = True
            self.wandb_project = None
            self.wandb_run_name = None
            self.wandb_logs_dir = None
            self.wandb_tags = "test,integration"
            self.wandb_notes = "Testing wandb integration"
    
    class MockAccelerator:
        def __init__(self):
            self.is_main_process = True
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create mock arguments with temp directory
        model_args = MockArgs()
        data_args = MockArgs()
        training_args = MockArgs()
        training_args.output_dir = os.path.join(temp_dir, "llaoa_wandb_test_20250623_123456")
        
        accelerator = MockAccelerator()
        
        # Create wandb configuration
        wandb_tags = [tag.strip() for tag in training_args.wandb_tags.split(",")]
        wandb_config = WandbConfig(
            project_name=training_args.wandb_project,
            run_name=training_args.wandb_run_name,
            output_dir=training_args.output_dir,
            logs_dir=training_args.wandb_logs_dir,
            offline=training_args.wandb_offline,
            tags=wandb_tags,
            notes=training_args.wandb_notes
        )
        
        print(f"✓ Created wandb config")
        print(f"  Project: {wandb_config.project_name}")
        print(f"  Run name: {wandb_config.run_name}")
        print(f"  Logs dir: {wandb_config.logs_dir}")
        print(f"  Tags: {wandb_config.tags}")
        
        # Test initialization (in offline mode)
        wandb_run = initialize_wandb(
            wandb_config=wandb_config,
            model_args=model_args,
            data_args=data_args,
            training_args=training_args,
            accelerator=accelerator
        )
        
        if wandb_run is not None:
            print("✓ Wandb initialized successfully")
            
            # Test logging metrics
            test_metrics = {
                "train/loss": 0.5,
                "train/learning_rate": 1e-5,
                "train/epoch": 0,
                "train/step": 10
            }
            log_metrics(test_metrics, step=10)
            print("✓ Logged test metrics")
            
            # Test finishing wandb
            finish_wandb()
            print("✓ Finished wandb run")
        else:
            print("⚠ Wandb initialization returned None (expected in some environments)")
        
        return True


def test_logs_directory_creation():
    """Test that logs directory is created correctly."""
    print("\nTesting logs directory creation...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = os.path.join(temp_dir, "llaoa_projector_training_20250623_123456")
        
        config = WandbConfig(output_dir=output_dir)
        
        # Check that logs directory was created
        if config.logs_dir.exists():
            print(f"✓ Logs directory created: {config.logs_dir}")
        else:
            print(f"✗ Logs directory not created: {config.logs_dir}")
            return False
            
        # Test with custom logs directory
        custom_logs_dir = os.path.join(temp_dir, "custom_logs")
        config2 = WandbConfig(output_dir=output_dir, logs_dir=custom_logs_dir)
        
        if config2.logs_dir.exists():
            print(f"✓ Custom logs directory created: {config2.logs_dir}")
        else:
            print(f"✗ Custom logs directory not created: {config2.logs_dir}")
            return False
            
        return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing LLaOA Wandb Integration")
    print("=" * 60)
    
    tests = [
        test_wandb_config,
        test_logs_directory_creation,
        test_wandb_initialization,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} FAILED with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Wandb integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
