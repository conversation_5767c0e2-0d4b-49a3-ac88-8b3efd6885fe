#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Weights & Biases (wandb) utilities for LLaOA training.
Handles offline mode setup, project naming, logs directory, and initialization.
"""

import os
import sys
import json
import psutil
import platform
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    wandb = None

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None


class WandbConfig:
    """Configuration class for Weights & Biases integration."""
    
    def __init__(
        self,
        project_name: Optional[str] = None,
        run_name: Optional[str] = None,
        output_dir: str = "./checkpoints",
        logs_dir: Optional[str] = None,
        offline: bool = True,
        tags: Optional[list] = None,
        notes: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize wandb configuration.
        
        Args:
            project_name: Project name for wandb. If None, derived from run_name
            run_name: Run name for wandb. If None, derived from output_dir
            output_dir: Training output directory
            logs_dir: Directory to store wandb logs. If None, uses <output_dir>/logs
            offline: Whether to run wandb in offline mode
            tags: List of tags for the run
            notes: Notes for the run
            config: Configuration dictionary to log
        """
        self.output_dir = Path(output_dir)
        self.offline = offline
        self.tags = tags or []
        self.notes = notes
        self.config = config or {}
        
        # Set project name based on run name if not provided
        if project_name is None:
            if run_name is not None:
                self.project_name = run_name
            else:
                self.project_name = self._extract_project_name_from_output_dir()
        else:
            self.project_name = project_name
            
        # Set run name based on output directory if not provided
        if run_name is None:
            self.run_name = self._extract_run_name_from_output_dir()
        else:
            self.run_name = run_name
            
        # Set logs directory
        if logs_dir is not None:
            # Use provided logs directory
            self.logs_dir = Path(logs_dir)
        else:
            # Default to output_dir/logs for backward compatibility
            self.logs_dir = self.output_dir / "logs"
            
        # Ensure logs directory exists
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
    def _find_llaoa_root(self) -> Path:
        """Find the LLaOA root directory."""
        current_path = Path(__file__).parent.absolute()
        
        # Walk up the directory tree to find LLaOA root
        while current_path.parent != current_path:
            if (current_path / "llaoa").exists() and (current_path / "pyproject.toml").exists():
                return current_path
            current_path = current_path.parent
            
        # Fallback to current directory
        return Path.cwd()
        
    def _extract_project_name_from_output_dir(self) -> str:
        """Extract project name from output directory path."""
        # Get the folder name from output directory
        folder_name = self.output_dir.name
        
        # Extract meaningful project name
        if "llaoa" in folder_name.lower():
            # For names like "llaoa_projector_training_20250623_050153"
            parts = folder_name.split("_")
            if len(parts) >= 3:
                return "_".join(parts[:3])  # e.g., "llaoa_projector_training"
            else:
                return folder_name
        else:
            return "llaoa_training"
            
    def _extract_run_name_from_output_dir(self) -> str:
        """Extract run name from output directory path."""
        folder_name = self.output_dir.name
        
        # Use the full folder name as run name
        return folder_name
        
    def setup_environment(self):
        """Set up wandb environment variables for offline mode."""
        if self.offline:
            os.environ["WANDB_MODE"] = "offline"
            os.environ["WANDB_DIR"] = str(self.logs_dir)
            os.environ["WANDB_CACHE_DIR"] = str(self.logs_dir / "cache")
            os.environ["WANDB_CONFIG_DIR"] = str(self.logs_dir / "config")
            
        # Disable wandb console output to avoid conflicts with training logs
        os.environ["WANDB_SILENT"] = "true"
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for logging."""
        info = {
            "platform": platform.platform(),
            "python_version": sys.version,
            "cpu_count": psutil.cpu_count(),
            "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "hostname": platform.node(),
        }
        
        if TORCH_AVAILABLE and torch is not None:
            info.update({
                "torch_version": torch.__version__,
                "cuda_available": torch.cuda.is_available(),
                "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            })
            
            if torch.cuda.is_available():
                gpu_info = []
                for i in range(torch.cuda.device_count()):
                    gpu_info.append({
                        "device_id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_gb": round(torch.cuda.get_device_properties(i).total_memory / (1024**3), 2)
                    })
                info["gpu_info"] = gpu_info
                
        return info


def initialize_wandb(
    wandb_config: WandbConfig,
    model_args: Any = None,
    data_args: Any = None,
    training_args: Any = None,
    accelerator: Any = None
) -> Optional[Any]:
    """
    Initialize wandb for training logging.
    
    Args:
        wandb_config: WandbConfig instance
        model_args: Model arguments to log
        data_args: Data arguments to log
        training_args: Training arguments to log
        accelerator: Accelerator instance for distributed training
        
    Returns:
        wandb run object if successful, None otherwise
    """
    if not WANDB_AVAILABLE:
        print("Warning: wandb not available, skipping wandb initialization")
        return None
        
    # Only initialize wandb on main process
    if accelerator is not None and not accelerator.is_main_process:
        return None
        
    try:
        # Set up environment
        wandb_config.setup_environment()
        
        # Prepare config for logging
        config_dict = wandb_config.config.copy()
        
        # Add system information
        config_dict["system"] = wandb_config.get_system_info()
        
        # Add arguments if provided
        if model_args is not None:
            config_dict["model"] = {
                k: v for k, v in vars(model_args).items() 
                if not k.startswith('_')
            }
            
        if data_args is not None:
            config_dict["data"] = {
                k: v for k, v in vars(data_args).items() 
                if not k.startswith('_')
            }
            
        if training_args is not None:
            config_dict["training"] = {
                k: v for k, v in vars(training_args).items() 
                if not k.startswith('_')
            }
            
        # Initialize wandb
        run = wandb.init(
            project=wandb_config.project_name,
            name=wandb_config.run_name,
            config=config_dict,
            tags=wandb_config.tags,
            notes=wandb_config.notes,
            dir=str(wandb_config.logs_dir),
            mode='offline',
            reinit=True  # Allow multiple runs in same process
        )
        
        print(f"✓ Initialized wandb project: {wandb_config.project_name}")
        print(f"✓ Run name: {wandb_config.run_name}")
        print(f"✓ Logs directory: {wandb_config.logs_dir}")
        print(f"✓ Offline mode: {wandb_config.offline}")
        
        return run
        
    except Exception as e:
        print(f"Warning: Failed to initialize wandb: {e}")
        return None


def log_metrics(metrics: Dict[str, Union[float, int]], step: Optional[int] = None):
    """
    Log metrics to wandb.
    
    Args:
        metrics: Dictionary of metrics to log
        step: Optional step number
    """
    if not WANDB_AVAILABLE or wandb.run is None:
        return
        
    try:
        wandb.log(metrics, step=step)
    except Exception as e:
        print(f"Warning: Failed to log metrics to wandb: {e}")


def log_model_info(model: Any, tokenizer: Any = None):
    """
    Log model information to wandb.
    
    Args:
        model: Model to log information about
        tokenizer: Optional tokenizer to log information about
    """
    if not WANDB_AVAILABLE or wandb.run is None:
        return
        
    try:
        # Log model parameters
        if hasattr(model, 'parameters'):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            wandb.log({
                "model/total_parameters": total_params,
                "model/trainable_parameters": trainable_params,
                "model/trainable_percentage": 100 * trainable_params / total_params if total_params > 0 else 0
            })
            
        # Log tokenizer info
        if tokenizer is not None:
            wandb.log({
                "tokenizer/vocab_size": len(tokenizer) if hasattr(tokenizer, '__len__') else None,
                "tokenizer/model_max_length": getattr(tokenizer, 'model_max_length', None)
            })
            
    except Exception as e:
        print(f"Warning: Failed to log model info to wandb: {e}")


def finish_wandb():
    """Finish wandb run."""
    if not WANDB_AVAILABLE or wandb.run is None:
        return
        
    try:
        wandb.finish()
        print("✓ Finished wandb run")
    except Exception as e:
        print(f"Warning: Failed to finish wandb run: {e}")
