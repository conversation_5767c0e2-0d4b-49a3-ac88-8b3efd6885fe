import torch
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from torch.utils.data import Dataset
from ..train.llama2_preprocessing import preprocess_conversations, format_qa_as_conversation
from ..constants import IGNORE_INDEX

logger = logging.getLogger(__name__)

class OmicsQADataset(Dataset):
    def __init__(
        self,
        rna_seq_path: str,
        qa_json_path: str,
        tokenizer: Any,
        max_length: Optional[int] = None,
        sample_id_col: Optional[str] = "sample_id",
        include_raw_text: bool = False,
        scaler: Optional[Any] = None
    ):
        """
        Dataset for omics QA pairs with LLAMA2 chat template formatting.

        This dataset handles all data preprocessing including:
        - RNAseq data scaling and tensor conversion
        - LLAMA2 chat template formatting with [INST]/[/INST] and <<SYS>>/<<SYS>> tokens
        - Automatic <omics> token integration for multimodal training
        - Proper label masking (instructions masked with IGNORE_INDEX, responses kept for loss)

        Args:
            rna_seq_path: Path to RNAseq data (TSV/CSV)
            qa_json_path: Path to QA pairs JSON
            tokenizer: LLAMA2 tokenizer for text processing
            max_length: Maximum sequence length (if None, uses tokenizer.model_max_length)
            sample_id_col: Column name for sample ID in RNAseq data
            include_raw_text: Whether to include raw text in the output
            scaler: Optional scaler for preprocessing RNAseq data (from COMPASS model)

        Note:
            This dataset exclusively uses LLAMA2 chat template formatting.
        """
        self.tokenizer = tokenizer
        # Use tokenizer's model_max_length if max_length not specified
        tokenizer_max_length = getattr(tokenizer, 'model_max_length', None)
        
        if max_length is not None:
            self.max_length = max_length
        elif (tokenizer_max_length is not None and tokenizer_max_length != float('inf')):  # Filter out unreasonably large values
            # Use tokenizer's max_length, but cap at reasonable limit for training efficiency
            self.max_length = min(tokenizer_max_length, 4096)
        else:
            # Fallback to reasonable default
            self.max_length = 2048
            logger.warning(f"Tokenizer has invalid model_max_length ({tokenizer_max_length}), using fallback: {self.max_length}")
        
        logger.info(f"Using max_length: {self.max_length} (tokenizer.model_max_length: {tokenizer_max_length})")
        self.include_raw_text = include_raw_text
        self.sample_id_col = sample_id_col
        self.scaler = scaler

        # Load RNAseq data
        logger.info(f"Loading RNAseq data from {rna_seq_path}")
        self.rna_seq_df = pd.read_csv(rna_seq_path, sep='\t' if rna_seq_path.endswith('.tsv') else ',')
        logger.info(f"RNAseq data shape: {self.rna_seq_df.shape}")
        logger.info(f"RNAseq data columns (first 10): {self.rna_seq_df.columns.tolist()[:10]}")

        # Load QA pairs
        logger.info(f"Loading QA pairs from {qa_json_path}")
        self.qa_pairs = pd.read_json(qa_json_path)
        logger.info(f"QA pairs columns: {self.qa_pairs.columns.tolist()}")
        logger.info(f"QA pairs shape: {self.qa_pairs.shape}")

        # Keep only data from both files containing common sample_id
        common_sample_ids = set(self.rna_seq_df[self.sample_id_col].tolist()) & set(self.qa_pairs[self.sample_id_col].tolist())
        self.rna_seq_df = self.rna_seq_df.loc[self.rna_seq_df[self.sample_id_col].isin(common_sample_ids)].reset_index(drop=True)
        self.qa_pairs = self.qa_pairs[self.qa_pairs[self.sample_id_col].isin(common_sample_ids)].reset_index(drop=True)
        logger.info(f"After filtering: {len(self.qa_pairs)} QA pairs and {len(self.rna_seq_df)} RNAseq samples")
        
        # Preprocessing and optimization
        self._preprocess_rnaseq_data()
        self._create_sample_lookup()
        self._preprocess_qa_data()
        
        logger.info("Dataset initialization complete with optimizations")

    def _preprocess_rnaseq_data(self):
        """
        Preprocess RNAseq data: normalize entire dataset once and convert to tensors.
        Excludes sample_id column from scaling.
        """
        logger.info("Preprocessing RNAseq data...")
        
        # Separate sample IDs from features
        sample_ids = self.rna_seq_df[self.sample_id_col].values
        feature_columns = [col for col in self.rna_seq_df.columns if col != self.sample_id_col]
        feature_data = self.rna_seq_df[feature_columns]
        
        logger.info(f"Feature data shape: {feature_data.shape}")
        
        # Apply scaling if scaler is provided
        if self.scaler is not None:
            logger.info("Applying scaler to entire RNAseq dataset")
            try:
                # Scale the feature data (excluding sample_id)
                scaled_data = self.scaler.transform(feature_data).values
                logger.info("Successfully applied scaler to RNAseq data")
            except Exception as e:
                logger.error(f"Error applying scaler: {e}")
                logger.warning("Using raw data instead")
                scaled_data = feature_data.values
        else:
            logger.warning("No scaler provided - using raw RNAseq data")
            scaled_data = feature_data.values

        # Convert to tensor and store (use float16 for consistency with mixed precision training)
        self.omics_tensors = torch.tensor(scaled_data, dtype=torch.float16).cpu()
        self.sample_ids = sample_ids
        logger.info(f"Cached omics tensors shape: {self.omics_tensors.shape} on device: {self.omics_tensors.device}")

    def _create_sample_lookup(self):
        """
        Create efficient lookup for sample_id to tensor index mapping.
        Handles non-unique sample_ids by creating a mapping.
        """
        logger.info("Creating sample ID lookup table...")
        
        # Create mapping from sample_id to tensor row index
        self.sample_id_to_idx = {}
        for idx, sample_id in enumerate(self.sample_ids):
            self.sample_id_to_idx[sample_id] = idx
        logger.info(f"Created lookup for {len(self.sample_id_to_idx)} unique samples")

    def _preprocess_qa_data(self):
        """
        Preprocess QA data using LLAMA2 chat template format.
        Uses raw questions and answers with LLAMA2 conversation template.
        Caches the processed output for faster repeated runs.
        """
        import hashlib
        import os
        import pickle

        logger.info("Preprocessing QA data with LLAMA2 chat template and caching...")

        # Prepare a unique hash for the current QA pairs and max_length
        def compute_hash(qa_pairs, max_length):
            # Use question/answer text and max_length for hash
            m = hashlib.sha256()
            for _, row in qa_pairs.iterrows():
                m.update(str(row['question']).encode('utf-8'))
                m.update(str(row['answer']).encode('utf-8'))
            m.update(str(max_length).encode('utf-8'))
            return m.hexdigest()

        cache_dir = getattr(self, "cache_dir", ".cache_qa_preprocessing")
        os.makedirs(cache_dir, exist_ok=True)
        hashid = compute_hash(self.qa_pairs, self.max_length)
        cache_path = os.path.join(cache_dir, f"qa_preproc_{hashid}.pkl")

        if os.path.exists(cache_path):
            logger.info(f"Loading preprocessed QA data from cache: {cache_path}")
            with open(cache_path, "rb") as f:
                cache = pickle.load(f)
            self.input_ids_list = cache['input_ids_list']
            self.labels_list = cache['labels_list']
            self.formatted_questions = cache['formatted_questions']
            self.formatted_answers = cache['formatted_answers']
        else:
            # Initialize storage lists
            self.input_ids_list = []
            self.labels_list = []
            self.formatted_questions = []
            self.formatted_answers = []

            for _, row in self.qa_pairs.iterrows():
                # Use raw question/answer - LLAMA2 template handles all formatting
                raw_question = row['question']
                raw_answer = row['answer']

                # Store raw text for inspection
                self.formatted_questions.append(raw_question)
                self.formatted_answers.append(raw_answer)

                # Create conversation format and process with LLAMA2 template
                conversation = format_qa_as_conversation(raw_question, raw_answer, has_omics=True)
                processed = preprocess_conversations([conversation], self.tokenizer, has_omics=True)
                input_ids = processed['input_ids'][0]
                labels = processed['labels'][0]

                # Ensure they don't exceed max_length
                if len(input_ids) > self.max_length:
                    input_ids = input_ids[:self.max_length]
                    labels = labels[:self.max_length]

                self.input_ids_list.append(input_ids)
                self.labels_list.append(labels)

            # Save to cache
            with open(cache_path, "wb") as f:
                pickle.dump({
                    'input_ids_list': self.input_ids_list,
                    'labels_list': self.labels_list,
                    'formatted_questions': self.formatted_questions,
                    'formatted_answers': self.formatted_answers,
                }, f)
            logger.info(f"Saved preprocessed QA data to cache: {cache_path}")

        logger.info(f"Pre-processed {len(self.input_ids_list)} QA pairs with LLAMA2 format")
        if len(self.input_ids_list) > 0:
            logger.info(f"Input IDs shape example: {self.input_ids_list[0].shape}")
            logger.info(f"Labels shape example: {self.labels_list[0].shape}")

            # Validation
            self._validate_preprocessing()
    
    def _validate_preprocessing(self):
        """Validate that LLAMA2 preprocessing was done correctly."""
        logger.info("Validating LLAMA2 preprocessing...")

        for idx in range(min(3, len(self.input_ids_list))):  # Check first 3 examples
            input_ids = self.input_ids_list[idx]
            labels = self.labels_list[idx]

            # Basic checks
            assert len(input_ids) == len(labels), f"Length mismatch at idx {idx}: input_ids {len(input_ids)} vs labels {len(labels)}"
            assert len(input_ids) > 0, f"Empty input_ids at idx {idx}"
            assert len(labels) > 0, f"Empty labels at idx {idx}"

            # Check that labels contain both IGNORE_INDEX and valid tokens
            has_ignore_tokens = torch.any(labels == IGNORE_INDEX)
            has_valid_tokens = torch.any(labels != IGNORE_INDEX)
            assert has_ignore_tokens, f"Labels should contain IGNORE_INDEX tokens (for instructions) at idx {idx}"
            assert has_valid_tokens, f"Labels should contain valid tokens (for responses) for loss calculation at idx {idx}"

            # LLAMA2 format validation
            logger.debug(f"Validating LLAMA2 format for sample {idx}")
            try:
                # Decode to check LLAMA2 structure
                decoded_text = self.tokenizer.decode(input_ids, skip_special_tokens=False)

                # Check for LLAMA2 chat template structure
                assert "<s>" in decoded_text, f"LLAMA2 format should contain <s> token at idx {idx}"
                assert "[INST]" in decoded_text, f"LLAMA2 format should contain [INST] token at idx {idx}"
                assert "[/INST]" in decoded_text, f"LLAMA2 format should contain [/INST] token at idx {idx}"
                assert "<<SYS>>" in decoded_text, f"LLAMA2 format should contain <<SYS>> token at idx {idx}"
                assert "<</SYS>>" in decoded_text, f"LLAMA2 format should contain <</SYS>> token at idx {idx}"

                logger.debug(f"Sample {idx} LLAMA2 format validation passed")

            except Exception as e:
                logger.warning(f"Could not validate LLAMA2 format for sample {idx}: {e}")
                # Continue validation for other samples

        logger.info("LLAMA2 preprocessing validation completed!")

    def __len__(self):
        return len(self.qa_pairs)

    def __getitem__(self, idx):
        # Get QA data
        qa = self.qa_pairs.iloc[idx]
        sample_id = qa[self.sample_id_col]

        # Get omics tensor using lookup (much faster than pandas filtering)
        try:
            omics_idx = self.sample_id_to_idx[sample_id]
            omics_tensor = self.omics_tensors[omics_idx:omics_idx+1]  # Shape [1, num_genes]
            # Add sequence dimension for COMPASS compatibility: [1, num_genes] -> [1, 1, num_genes]
            omics_tensor = omics_tensor.unsqueeze(1)  # Shape [1, 1, num_genes]
            # Ensure omics tensor is on CPU and detached
            omics_tensor = omics_tensor.detach().cpu()
        except KeyError:
            raise KeyError(f"Sample ID {sample_id} not found in RNAseq data.")

        # Get pre-processed data (ensure they're on CPU and detached for DataLoader)
        input_ids = self.input_ids_list[idx].detach().cpu()
        labels = self.labels_list[idx].detach().cpu()

        result = {
            'omics': omics_tensor,  # Tensor with shape [1, 1, num_genes] on CPU
            'input_ids': input_ids,  # LLAMA2 formatted conversation tokens
            'labels': labels,        # -100 for instruction tokens, response tokens for loss
        }

        # Include raw text if requested
        if self.include_raw_text:
            result.update({
                'sample_id': sample_id,
                'question': self.formatted_questions[idx],
                'answer': self.formatted_answers[idx],
                'original_question': qa['question'],
                'original_answer': qa['answer']
            })

        return result
    
    def inspect_sample(self, idx: int = 0) -> Dict[str, Any]:
        """
        Inspect a sample for debugging purposes.

        Args:
            idx: Index of the sample to inspect

        Returns:
            Dictionary with decoded text and tensor information
        """
        if idx >= len(self):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self)}")

        sample = self[idx]

        # Decode input_ids and labels
        input_text = self.tokenizer.decode(sample['input_ids'], skip_special_tokens=False)

        # Decode only non-masked labels (response tokens) for comparison
        response_tokens = sample['labels'][sample['labels'] != IGNORE_INDEX]
        response_text = self.tokenizer.decode(response_tokens, skip_special_tokens=False) if len(response_tokens) > 0 else ""

        # Count instruction vs response tokens
        num_instruction_tokens = (sample['labels'] == IGNORE_INDEX).sum().item()
        num_response_tokens = (sample['labels'] != IGNORE_INDEX).sum().item()

        return {
            'sample_id': sample.get('sample_id', 'N/A'),
            'omics_shape': sample['omics'].shape,
            'input_ids_length': len(sample['input_ids']),
            'labels_length': len(sample['labels']),
            'num_instruction_tokens': num_instruction_tokens,
            'num_response_tokens': num_response_tokens,
            'llama2_conversation': input_text,
            'response_only': response_text,
            'raw_question': sample.get('original_question', 'N/A'),
            'raw_answer': sample.get('original_answer', 'N/A'),
        }

    @classmethod
    def from_config(cls, config: Dict[str, Any], tokenizer: Any, scaler: Optional[Any] = None) -> 'OmicsQADataset':
        """
        Create a dataset from a configuration dictionary.

        Note: Legacy parameters (prompt_template, answer_prefix, use_llama2_format,
        legacy_format_compatibility) are ignored as the dataset now exclusively
        uses LLAMA2 chat template formatting.
        """
        # Warn about deprecated config parameters
        deprecated_params = ['prompt_template', 'answer_prefix', 'use_llama2_format', 'legacy_format_compatibility']
        found_deprecated = [param for param in deprecated_params if param in config]
        if found_deprecated:
            logger.warning(f"Deprecated config parameters ignored: {found_deprecated}. "
                         f"Dataset now exclusively uses LLAMA2 chat template formatting.")

        return cls(
            rna_seq_path=config['rna_seq_path'],
            qa_json_path=config['qa_json_path'],
            tokenizer=tokenizer,
            max_length=config.get('max_length'),  # None if not specified, will use tokenizer's max_length
            sample_id_col=config.get('sample_id_col', 'sample_id'),
            include_raw_text=config.get('include_raw_text', False),
            scaler=scaler
        )