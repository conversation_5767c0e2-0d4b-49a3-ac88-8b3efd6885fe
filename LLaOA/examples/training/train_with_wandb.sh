#!/bin/bash

# =============================================================================
# LLaOA Training with Weights & Biases (wandb) Integration
# =============================================================================
# This script demonstrates how to train LLaOA with comprehensive wandb logging
# including offline mode, custom project naming, and metrics tracking.
#
# Features:
# - Offline wandb mode (no internet required)
# - Automatic project naming based on checkpoint folder
# - Comprehensive metrics logging (loss, learning rate, system metrics)
# - Logs saved in LLaOA/logs directory
# - Compatible with existing LLAMA2 chat template and omics processing
# =============================================================================

set -e  # Exit on any error

# =============================================================================
# Configuration
# =============================================================================

echo "🚀 LLaOA Training with Wandb Integration"
echo "========================================"

# Model paths
COMPASS_MODEL_PATH="./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
LANGUAGE_MODEL_PATH="./models/llama-2-7b-chat-hf"

# Data paths
RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
QA_JSON_PATH="./data/dataset_01/qa_pairs.json"

# Training configuration
OUTPUT_DIR="./checkpoints/llaoa_wandb_training_$(date +%Y%m%d_%H%M%S)"
NUM_EPOCHS=3
BATCH_SIZE=4
LEARNING_RATE=1e-5
EVAL_STEPS=50
SAVE_STEPS=100
LOGGING_STEPS=10

# Wandb configuration
WANDB_PROJECT="llaoa_projector_training"  # Optional: will auto-derive if not set
WANDB_RUN_NAME=""  # Optional: will auto-derive if not set
WANDB_TAGS="projector-only,llama2,compass,genomics"
WANDB_NOTES="Training LLaOA projector with wandb logging in offline mode"

# =============================================================================
# Validation
# =============================================================================

echo "📋 Validating configuration..."

# Check if we're in the right directory
if [ ! -f "run_train.py" ]; then
    echo "❌ Error: run_train.py not found. Please run this script from the LLaOA directory."
    exit 1
fi

# Check model paths
if [ ! -f "$COMPASS_MODEL_PATH" ]; then
    echo "❌ Error: COMPASS model not found at $COMPASS_MODEL_PATH"
    exit 1
fi

if [ ! -d "$LANGUAGE_MODEL_PATH" ]; then
    echo "❌ Error: Language model not found at $LANGUAGE_MODEL_PATH"
    exit 1
fi

# Check data paths
if [ ! -f "$RNA_SEQ_PATH" ]; then
    echo "❌ Error: RNA-seq data not found at $RNA_SEQ_PATH"
    exit 1
fi

if [ ! -f "$QA_JSON_PATH" ]; then
    echo "❌ Error: QA pairs not found at $QA_JSON_PATH"
    exit 1
fi

echo "✅ All paths validated successfully"

# =============================================================================
# Environment Setup
# =============================================================================

echo "🔧 Setting up environment..."

# Create output directory
mkdir -p "$OUTPUT_DIR"
echo "✅ Created output directory: $OUTPUT_DIR"

# Create logs directory (wandb will use this)
mkdir -p "./logs"
echo "✅ Ensured logs directory exists: ./logs"

# =============================================================================
# Training Command
# =============================================================================

echo "🎯 Starting LLaOA training with wandb integration..."
echo ""
echo "Configuration Summary:"
echo "  📁 Output Directory: $OUTPUT_DIR"
echo "  🤖 Language Model: $LANGUAGE_MODEL_PATH"
echo "  🧬 COMPASS Model: $COMPASS_MODEL_PATH"
echo "  📊 RNA-seq Data: $RNA_SEQ_PATH"
echo "  ❓ QA Pairs: $QA_JSON_PATH"
echo "  📈 Wandb Project: $WANDB_PROJECT"
echo "  🏷️  Wandb Tags: $WANDB_TAGS"
echo "  📝 Wandb Notes: $WANDB_NOTES"
echo "  🔄 Epochs: $NUM_EPOCHS"
echo "  📦 Batch Size: $BATCH_SIZE"
echo "  📚 Learning Rate: $LEARNING_RATE"
echo ""

# Run training with wandb integration
uv run python run_train.py \
    --model-base "$LANGUAGE_MODEL_PATH" \
    --compass-model-path "$COMPASS_MODEL_PATH" \
    --feature-type gene_level \
    --projector-type mlp2x_gelu \
    --tune-projector-only \
    --rna-seq-path "$RNA_SEQ_PATH" \
    --qa-json-path "$QA_JSON_PATH" \
    --sample-id-col sample_id \
    --max-length 512 \
    --validation-split 0.1 \
    --output-dir "$OUTPUT_DIR" \
    --num-train-epochs $NUM_EPOCHS \
    --per-device-train-batch-size $BATCH_SIZE \
    --per-device-eval-batch-size $BATCH_SIZE \
    --gradient-accumulation-steps 4 \
    --learning-rate $LEARNING_RATE \
    --weight-decay 0.01 \
    --warmup-steps 100 \
    --lr-scheduler-type cosine \
    --logging-steps $LOGGING_STEPS \
    --eval-steps $EVAL_STEPS \
    --save-steps $SAVE_STEPS \
    --save-total-limit 3 \
    --dataloader-num-workers 4 \
    --seed 42 \
    --report-to wandb \
    --wandb-enabled \
    --wandb-offline \
    --wandb-project "$WANDB_PROJECT" \
    --wandb-run-name "$WANDB_RUN_NAME" \
    --wandb-tags "$WANDB_TAGS" \
    --wandb-notes "$WANDB_NOTES"

# =============================================================================
# Post-Training Summary
# =============================================================================

echo ""
echo "🎉 Training completed successfully!"
echo ""
echo "📁 Results saved to: $OUTPUT_DIR"
echo "📊 Wandb logs saved to: ./logs"
echo ""
echo "📈 Wandb Integration Summary:"
echo "  ✅ Offline mode: Enabled (no internet required)"
echo "  ✅ Project name: Auto-derived from checkpoint folder"
echo "  ✅ Metrics logged: Training/validation loss, learning rate, system metrics"
echo "  ✅ Logs directory: ./logs"
echo ""
echo "🔍 To view wandb logs offline:"
echo "  1. Navigate to the logs directory: cd logs"
echo "  2. Start wandb server: wandb offline"
echo "  3. Open browser to view dashboard"
echo ""
echo "📋 Training artifacts:"
echo "  - Model checkpoints: $OUTPUT_DIR/checkpoint-*"
echo "  - Final model: $OUTPUT_DIR/final"
echo "  - Wandb logs: ./logs/wandb"
echo ""
echo "✨ Happy training with LLaOA and wandb! ✨"
